"use client";

import { Box, Typography } from "@mui/material";
import Image from "next/image";
import { useRouter } from "next/navigation";
import MedicationIcon from "@mui/icons-material/Medication";
import { getThumborUrl } from "@/app/utils/getThumborUrl";
import { useTheme } from "@emotion/react";

const MWebHighlightsWidgetChainTemp3 = ({ highlights }) => {
  const router = useRouter();
  const theme = useTheme();
  const slicedHighlights = highlights.slice(0, 6); // Reduce to 6 for better mobile layout

  const onhighlightItemClick = (type, url) => {
    if (type === 0) {
      // Internal redirect
      router.push(url);
    } else if (type === 1) {
      // External redirect, open in a new tab
      window.open(url, "_blank", "noopener,noreferrer");
    }
  };

  return (
    <Box
      sx={{
        backgroundColor: theme.palette.primary.main,
        padding: "24px 16px",
        display: "grid",
        gridTemplateColumns: "repeat(2, 1fr)",
        gap: "16px",
        position: "relative",
        minHeight: "120px",
      }}
    >
      {slicedHighlights.map((highlightItem) => {
        const {
          code = null,
          title: displayName = "",
          imageUrl: iconUrl = "",
          highlightsRedirection = {},
          shortDescription = ""
        } = highlightItem || {};
        const { redirectionUrl: url, redirectionType: type } =
          highlightsRedirection;
        return (
          <Box
            id={`highlightsCard-${code}`}
            key={code}
            sx={{
              display: "flex",
              flexDirection: "column",
              alignItems: "flex-start",
              cursor: "pointer",
              padding: "12px",
              transition: "all 0.3s ease",
              borderRadius: "8px",
              ":hover": {
                backgroundColor: `rgba(255, 255, 255, 0.1)`,
                transform: "translateY(-2px)",
                boxShadow: "0 4px 12px rgba(0,0,0,0.1)",
              }
            }}
            onClick={() => onhighlightItemClick(type, url)}
          >
            {/* Icon */}
            <Box sx={{
              position: "relative",
              marginBottom: "8px",
              display: "flex",
              alignItems: "center",
              justifyContent: "flex-start"
            }}>
              {iconUrl ? (
                <Image
                  alt="service-icon"
                  src={getThumborUrl(iconUrl, 24, 24)}
                  height={24}
                  width={24}
                  style={{ filter: "brightness(0) invert(1)" }}
                />
              ) : (
                <MedicationIcon
                  sx={{ fontSize: "24px", color: "white" }}
                />
              )}
            </Box>

            {/* Title */}
            <Typography
              sx={{
                fontWeight: 500,
                color: "white",
                fontSize: "14px",
                textAlign: "left",
                marginBottom: "4px",
                lineHeight: 1.2,
                overflow: "hidden",
                textOverflow: "ellipsis",
                display: "-webkit-box",
                WebkitLineClamp: 2,
                WebkitBoxOrient: "vertical",
              }}
            >
              {displayName || ""}
            </Typography>

            {/* Description */}
            {shortDescription && (
              <Typography
                sx={{
                  color: "white",
                  opacity: 0.85,
                  fontSize: "12px",
                  textAlign: "left",
                  lineHeight: 1.2,
                  fontWeight: 400,
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  display: "-webkit-box",
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: "vertical",
                }}
              >
                {shortDescription}
              </Typography>
            )}
          </Box>
        );
      })}
    </Box>
  );
};

export default MWebHighlightsWidgetChainTemp3;
