"use client";

import {Box, Typography} from "@mui/material";
import Image from "next/image";
import {useRouter} from "next/navigation";
import MedicationIcon from "@mui/icons-material/Medication";
import {getThumborUrl} from "@/app/utils/getThumborUrl";
import { useTheme } from "@emotion/react";
import AddIcon from "@mui/icons-material/Add";

const WebHighlightsWidgetChainTemp3 = ({highlights}) => {
    const router = useRouter();
    const theme = useTheme();
    const slicedHighlights = highlights.slice(0, 3); // Limit to 3 items for better layout
    const onhighlightItemClick = (type, url) => {
        if (type === 0) {
            // Internal redirect
            router.push(url);
        } else if (type === 1) {
            // External redirect, open in a new tab
            window.open(url, "_blank", "noopener,noreferrer");
        }
    };

    return (
        <Box
            sx={{
                display: "flex",
                alignItems: "center",
                backgroundColor: theme.palette.primary.main,
                borderRadius: "12px",
                padding: "24px 32px",
                gap: "32px",
                boxShadow: "0 8px 32px rgba(0, 0, 0, 0.12)",
                minHeight: "120px",
                overflow: "hidden",
                position: "relative",
                "&::before": {
                    content: '""',
                    position: "absolute",
                    top: 0,
                    right: 0,
                    width: "100px",
                    height: "100%",
                    background: `linear-gradient(90deg, transparent, ${theme.palette.secondary.main}20)`,
                    pointerEvents: "none"
                }
            }}
        >
            {/* Main Heading */}
            <Box sx={{
                minWidth: "280px",
                zIndex: 1
            }}>
                <Typography
                    variant="h4"
                    sx={{
                        color: "white",
                        fontWeight: 600,
                        fontSize: { xs: "20px", md: "24px" },
                        lineHeight: 1.3,
                        marginBottom: "8px"
                    }}
                >
                    Simplify your wellness journey with a click
                </Typography>
                <Typography
                    sx={{
                        color: "white",
                        opacity: 0.9,
                        fontSize: "14px",
                        fontWeight: 400
                    }}
                >
                    Access our comprehensive healthcare services
                </Typography>
            </Box>

            {/* Highlight Items */}
            <Box sx={{
                display: "flex",
                gap: "24px",
                flex: 1,
                justifyContent: "flex-end",
                zIndex: 1
            }}>
                {slicedHighlights.map((highlightItem) => {
                    const {
                        code = null,
                        title: displayName = "",
                        imageUrl: iconUrl = "",
                        highlightsRedirection = {},
                        description = ""
                    } = highlightItem || {};
                    const {redirectionUrl: url, redirectionType: type} =
                        highlightsRedirection;

                    // Default descriptions for common healthcare services
                    const getDefaultDescription = (title) => {
                        const lowerTitle = title.toLowerCase();
                        if (lowerTitle.includes('doctor') || lowerTitle.includes('physician')) {
                            return "For exceptional care & expertise";
                        } else if (lowerTitle.includes('appointment') || lowerTitle.includes('book')) {
                            return "For world-class assistance";
                        } else if (lowerTitle.includes('opinion') || lowerTitle.includes('consultation')) {
                            return "For well-informed medical decisions";
                        } else if (lowerTitle.includes('emergency') || lowerTitle.includes('urgent')) {
                            return "For immediate medical attention";
                        } else {
                            return "For comprehensive healthcare services";
                        }
                    };

                    return (
                        <Box
                            id={`highlightsCard-${code}`}
                            key={code}
                            sx={{
                                display: "flex",
                                flexDirection: "column",
                                alignItems: "center",
                                cursor: "pointer",
                                padding: "16px",
                                borderRadius: "8px",
                                transition: "all 0.3s ease",
                                minWidth: "160px",
                                maxWidth: "180px",
                                "&:hover": {
                                    transform: "translateY(-4px)",
                                    backgroundColor: "rgba(255, 255, 255, 0.1)"
                                }
                            }}
                            onClick={() => onhighlightItemClick(type, url)}
                        >
                            {/* Icon with Plus */}
                            <Box sx={{
                                position: "relative",
                                marginBottom: "12px",
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center"
                            }}>
                                {iconUrl ? (
                                    <Image
                                        alt="service-icon"
                                        src={getThumborUrl(iconUrl, 40, 40)}
                                        height={40}
                                        width={40}
                                        style={{ filter: "brightness(0) invert(1)" }}
                                    />
                                ) : (
                                    <MedicationIcon
                                        sx={{fontSize: "40px", color: "white"}}
                                    />
                                )}
                                <AddIcon
                                    sx={{
                                        position: "absolute",
                                        top: "-8px",
                                        right: "-8px",
                                        fontSize: "20px",
                                        color: "white",
                                        backgroundColor: theme.palette.secondary.main,
                                        borderRadius: "50%",
                                        padding: "2px"
                                    }}
                                />
                            </Box>

                            {/* Title */}
                            <Typography
                                sx={{
                                    fontWeight: 600,
                                    color: "white",
                                    fontSize: "16px",
                                    textAlign: "center",
                                    marginBottom: "4px",
                                    lineHeight: 1.2
                                }}
                            >
                                {displayName || ""}
                            </Typography>

                            {/* Description */}
                            <Typography
                                sx={{
                                    color: "white",
                                    opacity: 0.9,
                                    fontSize: "12px",
                                    textAlign: "center",
                                    lineHeight: 1.3,
                                    fontWeight: 400
                                }}
                            >
                                {description || getDefaultDescription(displayName)}
                            </Typography>
                        </Box>
                    );
                })}
            </Box>
        </Box>
    );
};

export default WebHighlightsWidgetChainTemp3;
