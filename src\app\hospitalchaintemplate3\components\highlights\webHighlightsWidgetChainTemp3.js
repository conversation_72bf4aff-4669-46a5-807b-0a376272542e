"use client";

import {Box, Typography} from "@mui/material";
import Image from "next/image";
import {useRouter} from "next/navigation";
import MedicationIcon from "@mui/icons-material/Medication";
import {getThumborUrl} from "@/app/utils/getThumborUrl";
import { useTheme } from "@emotion/react";
import AddIcon from "@mui/icons-material/Add";

const WebHighlightsWidgetChainTemp3 = ({highlights}) => {
    console.log("highlights", highlights);
    const router = useRouter();
    const theme = useTheme();
    const onhighlightItemClick = (type, url) => {
        if (type === 0) {
            // Internal redirect
            router.push(url);
        } else if (type === 1) {
            // External redirect, open in a new tab
            window.open(url, "_blank", "noopener,noreferrer");
        }
    };

    return (
        <Box
            sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "flex-start",
                backgroundColor: theme.palette.primary.main,
                padding: "20px 80px", // Remove horizontal padding to go full width
                gap: "20px",
                minHeight: "80px",
                maxwidth: "100vw", 
                position: "relative",
            }}
        >
            {/* Main Heading */}
            <Box sx={{
                zIndex: 1,
                paddingRight: "80px",
                borderRight: "1px solid rgba(255, 255, 255, 0.3)",
            }}>
                <Typography
                    variant="h4"
                    sx={{
                        color: "white",
                        fontWeight: 500,
                        fontSize: { xs: "18px", md: "22px" },
                        lineHeight: 1.3,
                        marginBottom: "4px"
                    }}
                >
                    Simplify your wellness journey with a click
                </Typography>
                <Typography
                    sx={{
                        color: "white",
                        opacity: 0.9,
                        fontSize: "15px",
                        fontWeight: 400
                    }}
                >
                    Access our comprehensive healthcare services
                </Typography>
            </Box>

            {/* Highlight Items */}
            <Box sx={{
                display: "flex",
                gap: "10px",
                alignItems: "center",
                zIndex: 1,
            }}>
                {highlights.map((highlightItem, index) => {
                    const {
                        code = null,
                        title: displayName = "",
                        imageUrl: iconUrl = "",
                        highlightsRedirection = {},
                        shortDescription = ""
                    } = highlightItem || {};
                    const {redirectionUrl: url, redirectionType: type} =
                        highlightsRedirection;

                    return (
                        <Box
                            id={`highlightsCard-${code}`}
                            key={code}
                            sx={{
                                display: "flex",
                                flexDirection: "column",
                                alignItems: "flex-start",
                                cursor: "pointer",
                                padding: "16px 16px",
                                transition: "all 0.3s ease",
                                minWidth: "140px",
                                ":hover": {
                                    backgroundColor: `rgba(255, 255, 255, 0.1)`,
                                    transform: "translateY(-2px)",
                                    boxShadow: "0 4px 12px rgba(0,0,0,0.1)",
                                    borderRadius: "8px",
                                }
                            }}
                            onClick={() => onhighlightItemClick(type, url)}
                        >
                            {/* Icon */}
                            <Box sx={{
                                position: "relative",
                                marginBottom: "8px",
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center"
                            }}>
                                {iconUrl ? (
                                    <Image
                                        alt="service-icon"
                                        src={getThumborUrl(iconUrl, 20, 20)}
                                        height={20}
                                        width={20}
                                        style={{ filter: "brightness(0) invert(1)" }}
                                    />
                                ) : (
                                    <MedicationIcon
                                        sx={{fontSize: "32px", color: "white"}}
                                    />
                                )}
                            </Box>

                            {/* Title */}
                            <Typography
                                sx={{
                                    fontWeight: 500,
                                    color: "white",
                                    fontSize: "18px",
                                    textAlign: "left",
                                    marginBottom: "2px",
                                    lineHeight: 1.2
                                }}
                            >
                                {displayName || ""}
                            </Typography>

                            {/* Description */}
                            <Typography
                                sx={{
                                    color: "white",
                                    opacity: 0.85,
                                    fontSize: "14px",
                                    textAlign: "left",
                                    lineHeight: 1.2,
                                    fontWeight: 400
                                }}
                            >
                                {shortDescription || ""}
                            </Typography>
                        </Box>
                    );
                })}
            </Box>
        </Box>
    );
};

export default WebHighlightsWidgetChainTemp3;
