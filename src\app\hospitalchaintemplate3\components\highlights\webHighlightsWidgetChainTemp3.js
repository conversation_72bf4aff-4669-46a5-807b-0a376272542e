"use client";

import {Box, Typography} from "@mui/material";
import Image from "next/image";
import {useRouter} from "next/navigation";
import MedicationIcon from "@mui/icons-material/Medication";
import {getThumborUrl} from "@/app/utils/getThumborUrl";
import { useTheme } from "@emotion/react";
import AddIcon from "@mui/icons-material/Add";

const WebHighlightsWidgetChainTemp3 = ({highlights}) => {
    const router = useRouter();
    const theme = useTheme();
    const slicedHighlights = highlights.slice(0, 3); // Limit to 3 items for better layout
    const onhighlightItemClick = (type, url) => {
        if (type === 0) {
            // Internal redirect
            router.push(url);
        } else if (type === 1) {
            // External redirect, open in a new tab
            window.open(url, "_blank", "noopener,noreferrer");
        }
    };

    return (
        <Box
            sx={{
                display: "flex",
                alignItems: "center",
                backgroundColor: theme.palette.primary.main,
                padding: "20px 40px",
                gap: "40px",
                minHeight: "80px",
                width: "100%",
                position: "relative"
            }}
        >
            {/* Main Heading */}
            <Box sx={{
                flex: 1,
                zIndex: 1
            }}>
                <Typography
                    variant="h4"
                    sx={{
                        color: "white",
                        fontWeight: 600,
                        fontSize: { xs: "18px", md: "20px" },
                        lineHeight: 1.3,
                        marginBottom: "4px"
                    }}
                >
                    Simplify your wellness journey with a click
                </Typography>
                <Typography
                    sx={{
                        color: "white",
                        opacity: 0.9,
                        fontSize: "13px",
                        fontWeight: 400
                    }}
                >
                    Access our comprehensive healthcare services
                </Typography>
            </Box>

            {/* Highlight Items */}
            <Box sx={{
                display: "flex",
                gap: "0px",
                alignItems: "center",
                zIndex: 1
            }}>
                {slicedHighlights.map((highlightItem, index) => {
                    const {
                        code = null,
                        title: displayName = "",
                        imageUrl: iconUrl = "",
                        highlightsRedirection = {},
                        description = ""
                    } = highlightItem || {};
                    const {redirectionUrl: url, redirectionType: type} =
                        highlightsRedirection;

                    // Default descriptions for common healthcare services
                    const getDefaultDescription = (title) => {
                        const lowerTitle = title.toLowerCase();
                        if (lowerTitle.includes('doctor') || lowerTitle.includes('physician')) {
                            return "For exceptional care & expertise";
                        } else if (lowerTitle.includes('appointment') || lowerTitle.includes('book')) {
                            return "For world-class assistance";
                        } else if (lowerTitle.includes('opinion') || lowerTitle.includes('consultation')) {
                            return "For well-informed medical decisions";
                        } else if (lowerTitle.includes('emergency') || lowerTitle.includes('urgent')) {
                            return "For immediate medical attention";
                        } else {
                            return "For comprehensive healthcare services";
                        }
                    };

                    return (
                        <Box
                            id={`highlightsCard-${code}`}
                            key={code}
                            sx={{
                                display: "flex",
                                flexDirection: "column",
                                alignItems: "center",
                                cursor: "pointer",
                                padding: "16px 24px",
                                transition: "all 0.3s ease",
                                minWidth: "140px",
                                borderLeft: index > 0 ? "1px solid rgba(255, 255, 255, 0.2)" : "none",
                                "&:hover": {
                                    backgroundColor: "rgba(255, 255, 255, 0.1)"
                                }
                            }}
                            onClick={() => onhighlightItemClick(type, url)}
                        >
                            {/* Icon with Plus */}
                            <Box sx={{
                                position: "relative",
                                marginBottom: "8px",
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center"
                            }}>
                                {iconUrl ? (
                                    <Image
                                        alt="service-icon"
                                        src={getThumborUrl(iconUrl, 32, 32)}
                                        height={32}
                                        width={32}
                                        style={{ filter: "brightness(0) invert(1)" }}
                                    />
                                ) : (
                                    <MedicationIcon
                                        sx={{fontSize: "32px", color: "white"}}
                                    />
                                )}
                                <AddIcon
                                    sx={{
                                        position: "absolute",
                                        top: "-6px",
                                        right: "-6px",
                                        fontSize: "16px",
                                        color: "white",
                                        backgroundColor: theme.palette.secondary.main,
                                        borderRadius: "50%",
                                        padding: "1px"
                                    }}
                                />
                            </Box>

                            {/* Title */}
                            <Typography
                                sx={{
                                    fontWeight: 600,
                                    color: "white",
                                    fontSize: "16px",
                                    textAlign: "center",
                                    marginBottom: "4px",
                                    lineHeight: 1.2
                                }}
                            >
                                {displayName || ""}
                            </Typography>

                            {/* Description */}
                            <Typography
                                sx={{
                                    color: "white",
                                    opacity: 0.9,
                                    fontSize: "12px",
                                    textAlign: "center",
                                    lineHeight: 1.3,
                                    fontWeight: 400
                                }}
                            >
                                {description || getDefaultDescription(displayName)}
                            </Typography>
                        </Box>
                    );
                })}
            </Box>
        </Box>
    );
};

export default WebHighlightsWidgetChainTemp3;
